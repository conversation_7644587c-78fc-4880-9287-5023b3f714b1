/*
  Default theme for BotUI
*/

$font-size: 14px !default;
$botui-width: 600px !default;
$text-color: #fff !default;
$primary-color: #37BC9B !default;
$secondary-color: #209175 !default;
$font-family: system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol" !default; // credit: CSS-Tricks/Github

.botui_action {
  width: 100%;
  max-width: 100%;
  padding: 7px 14px;
  display: inline-block;

  textarea,
  input:not([type='file']),
  select {
    border: 0;
    margin: 0 2px;
    border-radius: 0;
    padding: 5px 7px;
    font-size: $font-size;
    vertical-align: bottom;
    color: $secondary-color;
    font-family: $font-family;
    background-color: $text-color;
  }

  &.action_input {
    form {
      display: flex;
      justify-content: flex-end;
    }
  }
}

.botui_button {
  border: 0;
  margin: 0 2px;
  line-height: 1;
  cursor: pointer;
  font-weight: 500;
  padding: 7px 15px;
  color: $text-color;
  border-radius: 4px;
  font-size: $font-size;
  vertical-align: bottom;
  font-family: $font-family;
  background: $secondary-color;
  box-shadow: 2px 3px 4px 0 rgba(0,0,0,.25);

  &:hover,
  &:focus {
    box-shadow: 1px 2px 3px 0 rgba(0,0,0,.25);
  }
}

.botui_message_list {
  padding: 10px 20px 0;
}

.botui_action_container {
  padding: 10px 20px;
  font-family: $font-family;
}

.botui_message_content {
  width: auto;
  max-width: 75%;
  overflow: scroll;
  line-height: 1.3;
  padding: 7px 13px;
  color: $text-color;
  border-radius: 15px;
  font-size: $font-size;
  display: inline-block;
  font-family: $font-family;
  background-color: $primary-color;

  &.human { // if human = true
    float: right;
    color: $text-color;
    background-color: $secondary-color;
  }

  iframe {
    border: 0;
    width: 100%;
  }
}

.botui_app_container {
  width: 100%; // mobile-first
  height: 100%;
  line-height: 1;

  @media (min-width: $botui-width) {
    height: 500px;
    margin: 0 auto;
    width: $botui-width;
  }
  scrollbar-width: none;
}

.botui_container {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
}

.botui_message {
  margin: 10px 0;
  min-height: 20px;

  &:after {
    display: block;
    content: "";
    clear: both;
  }
}
.botui_message_container {
  width: 100%;
}

.botui_wait {
  line-height: 1.3;
}

.message_links {
  a {
    color: $text-color;
    word-break: break-all;
    text-decoration: underline;

    & + a {
      margin-left: 7px;
    }
  }
}
/*
  Animation of loading dots
*/
.loading_dot {
  width: .5rem;
  height: .5rem;
  border-radius: .5rem;
  display: inline-block;
  background-color: $primary-color;

  &:nth-last-child(1) {
    margin-left: .3rem;
    animation: loading .6s .3s linear infinite;
  }
  &:nth-last-child(2) {
    margin-left: .3rem;
    animation: loading .6s .2s linear infinite;
  }
  &:nth-last-child(3) {
    animation: loading .6s .1s linear infinite;
  }
}

@keyframes loading {
  0% {
    transform: translate(0, 0);
    background-color: $primary-color;
  }

  25% {
    transform: translate(0, -3px);
  }
  50% {
    transform: translate(0, 0px);
    background-color: $primary-color;
  }
  75% {
    transform: translate(0, 3px);
  }
  100% {
    transform: translate(0, 0px);
  }
}

.slide-fade-enter-done {
  transition: all .3s ease;
}

.slide-fade-enter,
.slide-fade-exit-done {
  opacity: 0;
  transform: translateX(-10px);
}