server {
  listen 80;

  gzip on;
  gzip_min_length 1k;
  gzip_comp_level 9;
  gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
  gzip_vary on;
  gzip_disable "MSIE [1-6]\.";

  client_max_body_size 100m;

  root /usr/share/nginx/html;

  location / {
    try_files $uri $uri/ /index.html;
    # 静态文件缓存配置
    expires 1d;  # 设置静态文件缓存过期时间为1天
    add_header Cache-Control "public";
  }

  location /api/ {
    # 此处为后端地址
    proxy_pass ${BACK_API};
  }

  # 添加安全头设置
  add_header X-Content-Type-Options "nosniff";
  add_header X-Frame-Options "SAMEORIGIN";
  add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; frame-ancestors 'none';";

  # 防止服务隐藏文件
  location ~ /\. {
    deny all;
  }

  # 日志管理
  access_log /var/log/nginx/access.log;
  error_log /var/log/nginx/error.log;
}
