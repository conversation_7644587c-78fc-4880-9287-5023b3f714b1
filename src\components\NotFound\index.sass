$--white: #f2f5f5
$--black: #121212
$--blue: #308bd1
$--gray: #959695

.error
  height: 100vh
  display: flex
  align-items: center
  justify-content: center
  margin: 0 auto
  font-family: Montserrat, sans-serif
  color: $--white
  background-color: $--black

  .error__container
    max-width: 800px
    display: flex
    flex-direction: column
    justify-content: center
    align-items: center
    gap: 10px
    margin: 0 10px

    .error__code
      width: 100%
      margin: 0 auto
      font-size: 6rem
      font-weight: 800
      display: flex
      justify-content: center
      align-items: center
      gap: 10px

    p:first-of-type
      animation: fall 1s linear
      transform: rotateZ(-20deg)

    p:last-of-type
      animation: fall 1.8s linear
      transform: rotateZ(8deg)

    p:nth-of-type(2)
      color: var(--blue)
      animation: bounce 1.2s 1.8s linear forwards
      opacity: 0

    .error__title
      font-size: 1.5rem
      font-weight: 700
      padding: 0 3%

    .error__description
      font-size: 0.9rem
      text-align: justify
      line-height: 1.6
      padding: 0 10%
      color: $--gray

    .action
      font: inherit
      padding: 10px 30px
      border: none
      border-radius: 20px
      cursor: pointer
      background-color: $--blue
      color: $--white

    .action:hover
      opacity: 0.8

    @media screen and (min-width: 48rem)
      .error__description
        padding: 0

  @keyframes fall
    0%
      transform: translateY(-100vh)

    100%
      transform: translateY(0)

  @keyframes bounce
    0%, 40%, 75%, 95%
      transform: translateY(0)
      opacity: 1

    15%
      transform: translateY(-25px)

    65%
      transform: translateY(-15px)

    85%
      transform: translateY(-5px)

    100%
      transform: rotateZ(45deg)
      opacity: 1
