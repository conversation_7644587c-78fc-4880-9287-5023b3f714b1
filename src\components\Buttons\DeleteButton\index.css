.noselect {
    width: 150px;
    height: 50px;
    cursor: pointer;
    display: flex;
    align-items: center;
    background: red;
    border: none;
    border-radius: 5px;
    box-shadow: 1px 1px 3px rgba(0,0,0,0.15);
    background: #e62222;
}

.noselect, .noselect span {
    transition: 200ms;
}

.noselect .text {
    transform: translateX(35px);
    color: white;
    font-weight: bold;
}

.noselect .icon {
    position: relative;
    border-left: 1px solid #c41b1b;
    transform: translateX(70px);
    height: 40px;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.noselect svg {
    width: 15px;
    fill: #eee;
}

.noselect:hover {
    background: #ff3636;
}

.noselect:hover .text {
    color: transparent;
}

.noselect:hover .icon {
    width: 150px;
    border-left: none;
    transform: translateX(-10px);
}

.noselect:focus {
    outline: none;
}

.noselect:active .icon svg {
    transform: scale(0.8);
}