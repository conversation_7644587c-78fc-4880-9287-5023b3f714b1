name: Vite Build & Deploy

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v2

      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'  

      - name: Install Dependencies
        run: npm install 

      - name: Build project
        run: npm run build

      - name: Build Docker image
        run: |
          docker build -t ${{ secrets.DOCKERHUB_USERNAME }}/memory_blog:latest .

      - name: Log in to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_ACCESS_TOKEN }}

      - name: Push Docker image to Docker Hub
        run: |
          docker push ${{ secrets.DOCKERHUB_USERNAME }}/memory_blog:latest

      - name: Upload production-ready build files
        uses: actions/upload-artifact@v3  # 更新为 v3
        with:
          name: production-files
          path: ./dist  # 根据你的 Vite 配置，这里可能是 ./build，或其他目录

  deploy:
    name: Deploy
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/master'

    steps:
      - name: Download artifact
        uses: actions/download-artifact@v3  # 更新为 v3
        with:
          name: production-files
          path: ./dist  # 与上传时的目录保持一致

      - name: Deploy to gh-pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./dist  # 与上传和下载的目录保持一致
