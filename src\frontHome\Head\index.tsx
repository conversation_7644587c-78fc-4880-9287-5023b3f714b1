import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>fi<PERSON><PERSON><PERSON><PERSON>, Modal} from 'antd'
import './index.sass'
import {Key, ReactElement, ReactNode, ReactPortal, useEffect, useState} from "react";
import {useNavigate} from "react-router-dom";
import {debounce} from 'lodash';
import Switch from "../../components/Switch";
import SearchButton2 from "../../components/Buttons/SearchButton2";
import TopMao from "../../components/TopMao";
import {fetchCategories} from "../../store/components/categories.tsx";
import {useDispatch, useSelector} from "react-redux";
import {fetchTags} from "../../store/components/tags.tsx";
import {fetchSocial, fetchUserInfo} from "../../store/components/user.tsx";
import {fetchNoteList} from "../../store/components/note.tsx";
import UserState from "../../interface/UserState";
import '../main.css'
import MoonToSun from "../MoonToSun";
interface HeadProps {
    setDark: (value: (((prevState: boolean) => boolean) | boolean)) => void,
    isDark: boolean,
    scrollHeight: number
}

const Head = ({ setDark, isDark, scrollHeight }: HeadProps) => {
    const [showStatus, setShowStatus] = useState(false);
    const [phoneBarShow, setPhoneBarShow] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const [isLogin, setLogin] = useState(0)
    const dispatch = useDispatch()
    const navigate = useNavigate();
    const [animation,setAnimation] = useState('');
    const categoryList = useSelector((state: any) => state.categories.categories)
    const avatar = useSelector((state:{user:UserState}) => state.user.avatar)
    const blogTitle = useSelector((state:{user:{blogTitle: string}}) => state.user.blogTitle)

    useEffect(() => {
        console.log('Head 组件正在初始化...');
        // 临时注释掉 API 调用，用于调试
        // dispatch<any>(fetchCategories())
        // dispatch<any>(fetchTags())
        // dispatch<any>(fetchUserInfo())
        // dispatch<any>(fetchNoteList())
        // dispatch<any>(fetchSocial())
        const status = localStorage.getItem('tokenKey')
        if (status !== null) {
            setLogin(1)
        }

    }, []);

    // 定义防抖函数，设置延迟时间为 300 毫秒
    const startAnimationDebounced = debounce(() => {
        setShowStatus(true);
    }, 300);

    const handleMouseEnter = () => {
        startAnimationDebounced();
        setIsHovered(true);
    };

    const handleMouseLeave = () => {
        setShowStatus(false);
        setIsHovered(false);
    };

    const [isModalOpen, setIsModalOpen] = useState(false);

    const showModal = () => {
        setIsModalOpen(true);
    };


    const handleCancel = () => {
        setIsModalOpen(false);
    };

    const handleModeSwitch = () => {
        setDark(!isDark)
        setAnimation(isDark === true ? "sun" : "moon");
        localStorage.setItem("isDarkMode", JSON.stringify(!isDark));
    };

    // 临时简化 Head 组件，用于调试
    return (
        <header style={{
            display: 'flex',
            flexDirection: 'row',
            position: 'sticky',
            width: '100%',
            top: 0,
            zIndex: '999',
            backgroundColor: isDark ? '#333' : '#fff',
            padding: '10px 20px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
            <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%'}}>
                <h1 style={{color: isDark ? '#fff' : '#333', margin: 0}}>
                    {blogTitle || 'Memory Blog'}
                </h1>
                <nav style={{display: 'flex', gap: '20px'}}>
                    <button onClick={() => navigate('')} style={{background: 'none', border: 'none', color: isDark ? '#fff' : '#333', cursor: 'pointer'}}>
                        首页
                    </button>
                    <button onClick={() => navigate('about')} style={{background: 'none', border: 'none', color: isDark ? '#fff' : '#333', cursor: 'pointer'}}>
                        关于
                    </button>
                    <button onClick={handleModeSwitch} style={{background: 'none', border: 'none', color: isDark ? '#fff' : '#333', cursor: 'pointer'}}>
                        {isDark ? '🌞' : '🌙'}
                    </button>
                </nav>
            </div>
        </header>
    );
};

export default Head;
