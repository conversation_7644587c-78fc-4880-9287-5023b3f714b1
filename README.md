<h3 align="center"><img width="455" alt="Group 1 (6)" src="https://github.com/LinMoQC/Memory-Blog/assets/59323207/36d220c4-0d55-475f-abab-b25bad2ef6f1"></h3>

<p align="center"><strong>一个风格可爱的个人主题博客</strong></p>

<p align="center">本仓库为 <strong><a href="https://github.com/LinMoQC/Memory-Blog">Memory</a></strong> 的前端仓库，后端核心仓库在 <strong><a href="https://github.com/LinMoQC/Memory-Core">Memory Core</a></strong>  
</p>

<p align="center">欢迎你来体验 <strong>Memory</strong> 的魅力！ </p>

  
 ## :sparkles: 项目结构

```text
Memory Blog/
|-- src/
|   |-- apis
|   |-- assets
|   |-- components
|   |-- interface
|   |-- pages
|   |-- router

|   |-- store        // Redux States Management
|-- package.json
|-- README.md
```
  
 ## :wrench: 技术栈 

 - React
 - TypeScript
 - React Router
 - Reducer
 - Sass
 - Axios
 - Vite
 - Framer motion
  
 ## 📄 使用文档 
  
 ```bash
cd Memory-Blog
npm install
npm run dev

// 修改你的配置
/* .env  */
VITE_HTTP_BASEURL = 'http://127.0.0.1:8080'
VITE_CHAT_GPT_TOKEN = 'XXXXX'

npm run build  //打包
 ```
## ☀️ Docker
```bash
docker run -d -p 82:80 -e BACK_API=<backend_address> --name memory karensky/memory_blog:latest
```
  
 ## :camera: 截图 
  
### Home
![image](https://github.com/LinMoQC/Memory-Blog/assets/59323207/aa059596-aef0-496b-8003-4832cbd56ffa)

### Dark Mode
![image](https://github.com/LinMoQC/Memory-Blog/assets/59323207/9a3406d1-c5dd-492c-86ee-06ced76a50bb)

### Read
![image](https://github.com/LinMoQC/Memory-Blog/assets/59323207/161a71ab-6e47-4afd-a3eb-3669ee4a787a)


  
 <details> 
 <summary> 
 点击查看部分完整页面截图 
 </summary> 

### Login
![image](https://github.com/LinMoQC/Memory-Blog/assets/59323207/90c17b57-a5d5-46aa-80d5-e5103d2c9728)

### Admin
![image](https://github.com/LinMoQC/Memory-Blog/assets/59323207/7cddfac9-0ea2-4691-93dd-2a29640149ad)

</details>

## Star History 
  
 <a href="https://star-history.com/#aifuxi/fuxiaochen&Date"> 
  <picture> 
    <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=LinMoQC/Memory-Blog&type=Date&theme=dark" /> 
    <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=LinMoQC/Memory-Blog&type=Date" /> 
    <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=LinMoQC/Memory-Blog&type=Date" /> 
  </picture> 
 </a>
  
  
## :heart: 鸣谢 & 许可   
© 2024 林陌青川。本软件使用 GNU General Public License Version 2 许可。请参阅许可证文本以了解详细信息。
