.contain{
    height: 100vh;
}

.loading-overlay{
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slider {
    float: left;
}

body {
    min-height: 100vh;
    background-color: var(--body-color);
    transition: all 0.3s ease;
    list-style: none;
}

:root {
    --body-color: #E4E9F7;
    --shell-color: #eeeeee;
    --primary-color: #695CFE;
    --primary-color-light: #F6F5FF;
    --toggle-color: #DDD;
    --text-color: #707070;
}

/* 夜间模式 */
.dark{
    --body-color: #202224;
    --shell-color: #171717;
    --primary-color: #3a3b3c;
    --primary-color-light: #3a3b3c;
    --toggle-color: #fff;
    --text-color: #ccc;
}

.shell{
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 250px;
    padding: 10px 14px;
    background: var(--shell-color);
    transition: all 0.3s ease;
    z-index: 100;
}

.close {
    width: 88px;
    border-top-right-radius: 30px;
    border-bottom-right-radius: 30px;
}

.shell li{
    height: 50px;
    list-style: none;
    display: none;
    display: flex;
    align-items: center;
    margin-top: 10px;
}

.image,
.icon{
    min-width: 60px;
    border-radius: 6px;
}

.icon{
    min-width: 60px;
    border-radius: 6px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font: 300 23px "";
}

.text,
.icon{
    color: var(--text-color);
    transition: all 0.3s ease;
}

.text{
    white-space: nowrap;
    opacity: 1;
}

.shell.close .text{
    opacity: 0;
}

header{
    position: relative;
    top: 20px;
}

.image-text{
    display: flex;
    align-items: center;
}

.logo-text{
    display: flex;
    flex-direction: column;
}

.name{
    margin-top: 2px;
    font: 600 18px "";
}

.onesay{
    margin-top: 2px;
    font: 600 12px "";
    top: 5px;
}

.software{
    font-size: 20px;
    margin-top: -2px;
    display: block;
}

.image{
    display: flex;
    align-items: center;
    justify-content: center;
}

.image img{
    width: 45px;
    border-radius: 6px;
}

.toggle{
    position: absolute;
    top: 50%;
    right: -25px;
    transform: translateY(-50%) rotate(180deg);
    height: 25px;
    width: 25px;
    background-color: var(--primary-color);
    color: var(--shell-color);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    padding: 1px;
    font-size: 17px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.toggle{
    color: #ccc;
}

.shell.close .toggle{
    transform: translateY(-50%) rotate(0deg);
}

.menu{
    margin-top: 40px;
}

li.search-box {
    border-radius: 6px;
    background-color: var(--primary-color-light);
    cursor: pointer;
    transition: all 0.3s ease;
}

li.search-box input {
    height: 100%;
    width: 100%;
    outline: none;
    border: none;
    background-color: var(--primary-color-light);
    color: var(--text-color);
    border-radius: 6px;
    font-size: 17px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.shell li a{
    list-style: none;
    height: 100%;
    background-color: transparent;
    display: flex;
    align-items: center;
    height: 100%;
    width: 100%;
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.shell li a:hover{
    background-color: var(--primary-color);
}

.shell li:hover .icon,
.shell li:hover .text {
    color: var(--shell-color);
}

.menu-bar {
    height: calc(100% - 55px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow-y: scroll;
}

.menu-bar::-webkit-scrollbar {
    display: none;
}

.menu-bar .mode{
    border-radius: 6px;
    background-color: var(--primary-color-light);
    position: relative;
    transition: all 0.3s ease;
}

.menu-bar .mode .sun-moon {
    height: 50px;
    width: 60px;
}

.mode .text {
    left: 100px;
}

.mode .sun-moon i {
    position: absolute;
}

.mode .sun-moon i.sun{
    opacity: 0;
}

.toggle-switch{
    position: absolute;
    right: 0;
    height: 100%;
    min-width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    cursor: pointer;
}

.switch{
    position: relative;
    height: 22px;
    width: 40px;
    border-radius: 25px;
    background-color: var(--toggle-color);
    transition: all 0.3s ease;
}

.switch::before{
    content: '';
    position: absolute;
    height: 15px;
    width: 15px;
    border-radius: 50%;
    top: 50%;
    left: 5px;
    transform: translateY(-50%);
    background-color: var(--shell-color);
    transition: all 0.3s ease;
}

.dark .shell li a:hover .icon,
.dark .shell li a:hover .text {
    color: #ccc;
}

.dark .mode .sun-moon i.sun {
    opacity: 1;
}

.dark .mode .sun-moon i.moon {
    opacity: 1;
}

.dark .switch::before {
    left: 20px;
}

/*面板*/
.content{
    float: right;
    width: 100%;
    height: 100%;
    background-position: 0px 0px,0px 0px,0px 0px,0px 0px,0px 0px;
    /*background-image: radial-gradient(ellipse at top left, #65DAFF 0%, #8b91c7 50%, #FF9089 100%);*/
    background-color: #F5F5F5;
    background-size: cover;
    /*padding: 50px;*/
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.contentDark{
    background: radial-gradient(ellipse at top left, #373434 50%, #321e5d 100%);
    background-size: cover;
}

.Card {
    background-color: transparent;
    backdrop-filter: blur(10px);
    box-sizing: border-box;
    overflow: hidden;
    margin-left: 20px;
    border: 1px solid transparent;
    padding: 0;
}

.CardDark {
    background-color: transparent;
    border: 1px solid transparent;
    color: #cccccc;
}

.outDark {
    color: white;
}

.ant-card .ant-card-body{
    padding: 0;
    width: 100%;
    height: 100%;
}


/*选中状态*/
.nav_select{
    background-color: #b7dcfa;
    border-radius: 6px;
    transition: 0.5s;
}


.setting_btn{
    position: absolute;
    bottom: 20px;
    right: 20px;
}

/*.setting_btn:hover{*/
/*    justify-content: flex-start;*/
/*    align-items: flex-start;*/
/*    opacity: 1;*/
/*}*/

/*.setting_btn:not(:hover) {*/
/*    !* 鼠标移出时的样式 *!*/
/*    !* 你可以在这里设置移出时的样式，如果没有特殊需求，可以保持和初始样式一致 *!*/
/*    opacity: 0;*/

/*}*/