*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

*::-webkit-scrollbar{
    width: 0px;
    height: 0;
}

*::-webkit-scrollbar-thumb{
    background: linear-gradient(to bottom right, #4d7fff 0%, #1a56ff 100%);
    border-radius: 5px;
}

@media only screen and (max-width: 1200px){
    *::-webkit-scrollbar{
        width: 0;
        height: 0;
    }
}


:root{
    --front-body-color: linear-gradient(to left, rgba(243, 243, 243, 0.33), rgba(255, 255, 255, 0.33));
    --pic-background-cover: #fafafa;
    --pic-cover-color: linear-gradient(90deg,hsla(0,0%,98%,0) 0,hsla(0,0%,98%,.013) 8.1%,hsla(0,0%,98%,.049) 15.5%,hsla(0,0%,98%,.104) 22.5%,hsla(0,0%,98%,.175) 29%,hsla(0,0%,98%,.259) 35.3%,hsla(0,0%,98%,.352) 41.2%,hsla(0,0%,98%,.45) 47.1%,hsla(0,0%,98%,.55) 52.9%,hsla(0,0%,98%,.648) 58.8%,hsla(0,0%,98%,.741) 64.7%,hsla(0,0%,98%,.825) 71%,hsla(0,0%,98%,.896) 77.5%,hsla(0,0%,98%,.951) 84.5%,hsla(0,0%,98%,.987) 91.9%,#fff);
    --pic-background-cover: #fafafa;
    --pic-cover-color-m: linear-gradient(180deg, hsla(0, 0%, 98%, 0) 0, hsla(0, 0%, 98%, .013) 8.1%, hsla(0, 0%, 98%, .049) 15.5%, hsla(0, 0%, 98%, .104) 22.5%, hsla(0, 0%, 98%, .175) 29%, hsla(0, 0%, 98%, .259) 35.3%, hsla(0, 0%, 98%, .352) 41.2%, hsla(0, 0%, 98%, .45) 47.1%, hsla(0, 0%, 98%, .55) 52.9%, hsla(0, 0%, 98%, .648) 58.8%, hsla(0, 0%, 98%, .741) 64.7%, hsla(0, 0%, 98%, .825) 71%, hsla(0, 0%, 98%, .896) 77.5%, hsla(0, 0%, 98%, .951) 84.5%, hsla(0, 0%, 98%, .987) 91.9%, #fff);
    --font-title-color: black;
    --font-p-color: #898989;
    --box-shadow-color: 0 1px 35px -8px rgba(26, 26, 26, 0.6);
    --container-background-color: linear-gradient(to left, rgba(255, 255, 255,0.6), rgba(255, 255, 255,0.6));
    --category-hover-color: #b1cee3;
    --bg: #1a1e2d;
    --green: #a5ea9b;
    --pink: #ff61d8;
    --blue: #569cfa;
    --orange: #ffcc81;
    --cyan: #7ed1e2;
    --phoneBar-bg: #fff;
    --phoneBar-font-color: #717171;
}

.frontDark{
    --front-body-color: linear-gradient(to left, rgba(7, 7, 7, 0.33), rgba(7, 7, 7, 0.33));
    --pic-cover-color: linear-gradient(90deg, rgba(33, 33, 33, 0) 0, rgba(33, 33, 33, .013) 8.1%, rgba(33, 33, 33, .049) 15.5%, rgba(33, 33, 33, .104) 22.5%, rgba(33, 33, 33, .175) 29%, rgba(33, 33, 33, .259) 35.3%, rgba(33, 33, 33, .352) 41.2%, rgba(33, 33, 33, .45) 47.1%, rgba(33, 33, 33, .55) 52.9%, rgba(33, 33, 33, .648) 58.8%, rgba(33, 33, 33, .741) 64.7%, rgba(33, 33, 33, .825) 71%, rgba(33, 33, 33, .896) 77.5%, rgba(33, 33, 33, .951) 84.5%, rgba(33, 33, 33, .987) 91.9%, #212121);
    --pic-background-cover: #212121;
    --font-title-color: white;
    --font-p-color: #cccccc;
    --pic-cover-color-m: linear-gradient(180deg, rgba(33, 33, 33, 0) 0, rgba(33, 33, 33, .013) 8.1%, rgba(33, 33, 33, .049) 15.5%, rgba(33, 33, 33, .104) 22.5%, rgba(33, 33, 33, .175) 29%, rgba(33, 33, 33, .259) 35.3%, rgba(33, 33, 33, .352) 41.2%, rgba(33, 33, 33, .45) 47.1%, rgba(33, 33, 33, .55) 52.9%, rgba(33, 33, 33, .648) 58.8%, rgba(33, 33, 33, .741) 64.7%, rgba(33, 33, 33, .825) 71%, rgba(33, 33, 33, .896) 77.5%, rgba(33, 33, 33, .951) 84.5%, rgba(33, 33, 33, .987) 91.9%, #212121);
    --box-shadow-color: 0 1px 35px -8px rgba(229, 229, 229, 0.6);
    --container-background-color: linear-gradient(to left, rgba(0, 0, 0,0.6), rgba(0, 0, 0,0.6));
    --category-hover-color: #37708b;
    --phoneBar-bg: #232323;
    --phoneBar-font-color: #fff;
}

.lightBody{
    background: var(--front-body-color),url("https://img.picgo.net/2024/05/04/homeBg3c0f28da1a596884.jpeg") center;
    background-size: cover;
    backdrop-filter: blur(5px);
    background-attachment: fixed;
}