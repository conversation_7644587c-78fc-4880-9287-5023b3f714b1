//禁用状态伪元素去除
.ant-picker-calendar .ant-picker-cell::before
  display: none

.oneSay
  width: 95%
  height: 70%
  background-color: #fffbe8
  position: absolute
  left: 50%
  top: 50%
  margin-top: 10px
  transform: translate(-50%,-50%)
  border-radius: 10px
  box-shadow: 2px 12px 12px 2px #d7d6d6
  font-family: "Arial Black"
  font-weight: 600

  .onesay_content
    margin-top: 0
    margin-left: 30px
    margin-right: 30px
    overflow: hidden
    white-space: normal
    max-height: 6.5em
    line-height: 1.3em /* 行高 */
    //font-weight: 500
    text-overflow: 'ellipsis'

  .stick
    margin-left: -1px
    margin-top: -15px
    font-size: 20px
    display: inline-block
    transform: rotate(270deg)

.home
  width: 100%
  height: 100%
  display: flex
  flex-direction: row
  justify-content: space-between
  @media only screen and (max-width: 1530px)
    justify-content: space-around

  .ant-picker-calendar
    .ant-picker-panel
      .ant-picker-body
        border-radius: 10px

  .right
    display: flex
    justify-content: space-between
    flex-direction: column
    height: 100%
    width: 30%
    padding: 30px 30px 30px 0
    @media only screen and (max-width: 1530px)
      display: none

  .center
    @media only screen and (max-width: 768px)
      display: none

  .about_logo
    width: 89%
    height: 22%
    margin: 10% 0 0 15%
    display: flex
    flex-direction: column
    @media only screen and (max-width: 1530px)
      margin: 20% 0 0 20px
      transform: scale(1.4)
      align-items: center
      height: 15%
    @media only screen and (max-width: 768px)
      margin: 30% 0 0 5px

    .about_me
      display: flex
      flex-direction: row
      align-items: center
      img
        height: 8rem
        width: 8rem
        @media only screen and (max-width: 1500px)
          height: 5rem
          width: 5rem
      .typed
        font-weight: 600
        font-size: 20px
        margin-left: 10px
        @media only screen and (max-width: 768px)
          display: none

    .p_hidden
      @media only screen and (max-width: 1530px)
        display: none


  .ant-picker-calendar-mini
    border-radius: 10px
    background-color: transparent

    .ant-picker-calendar-header
      display: none

  .ant-picker-calendar .ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner
    background: rgb(197, 135, 188)
    border-radius: 50%
    &::before
      border: none

  .ant-picker-calendar
    .ant-picker-cell-disabled
      color: black
      background-color: transparent
    .ant-picker-panel
      .ant-picker-body
        background-color: #fff1eb

  .ant-card
    margin: 30px

    .ant-card-head
      border-bottom: none
      position: relative

      .dot:nth-child(3)
        width: 8px
        height: 8px
        background-color: #fc625d
        border-radius: 50%
        margin-right: 5px
        display: flex
        float: right
        margin-top: 6px

      .dot:nth-child(2)
        width: 8px
        height: 8px
        background-color: #fc625d
        border-radius: 50%
        margin-right: 5px
        display: flex
        float: right
        margin-top: 6px

      .dot:nth-child(1)
        width: 8px
        height: 8px
        background-color: #35cd4b
        border-radius: 50%
        margin-right: 5px
        display: flex
        float: right
        margin-top: 6px
  .cardInfo
    box-shadow: 0 1px 22px -8px rgba(26, 26, 26, .6)
    width: 100%
    height: 13%
    padding: 10px
    font-weight: 600
    overflow-y: scroll

    &::-webkit-scrollbar
      width: 5px
      height: 10px

    &::-webkit-scrollbar-thumb
      background: linear-gradient(to bottom right, #4d7fff 0%, #1a56ff 100%)
      border-radius: 5px


