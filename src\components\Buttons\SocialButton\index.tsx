import './index.sass'
import React from "react";

interface SocialButtonProps {
    SocialName: string;
    url: string
}
const SocialButton: React.FC<SocialButtonProps> = ({SocialName,url}) => {
    const SocialList = [
        {
            socialName: 'QQ',
            color: '#989ec5',
            svg: <svg height="1.6em" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">\n' +
                '  <path fill="#555" d="M18.4956198,13.6068179 C18.3618839,11.6758631 17.1236934,10.0574895 16.4079796,9.22028023 C16.5071482,8.97703308 16.7491195,7.56723963 15.8152348,6.60522894 C15.8169349,6.58211757 15.8169349,6.55900621 15.8169349,6.53705041 C15.8169349,2.74331935 13.2368519,0.0121334682 9.99998954,0 C6.7631272,0.0127112535 4.18304419,2.74331937 4.18304419,6.53705041 C4.18304419,6.55958399 4.18304419,6.58269536 4.18474423,6.60522894 C3.25085957,7.56723963 3.49339758,8.97703308 3.59199946,9.22028023 C2.87685235,10.0574895 1.63866184,11.6758631 1.50435927,13.6073956 C1.47999214,14.1152679 1.55536026,14.8548317 1.79166481,15.1835909 C2.08067038,15.5845732 2.87345231,15.1027011 3.44012988,13.8223314 C3.59766625,14.4157157 3.96147324,15.3216813 4.78542245,16.4708941 C3.4066959,16.8002311 3.01398834,18.2233136 3.4775306,19.0015889 C3.80450357,19.5493283 4.55308465,20 5.84340949,20 C8.13902036,20 9.15280655,19.354615 9.60558194,18.9045212 C9.6973837,18.8062978 9.83055293,18.7594973 9.99998954,18.7589195 C10.1694261,18.7589195 10.3025954,18.8062978 10.3943971,18.9045212 C10.8471725,19.354615 11.8609587,20 14.1560029,20 C15.4468944,20 16.1949088,19.5493283 16.5218818,19.0010111 C16.9859907,18.2233136 16.5927165,16.8002311 15.2151233,16.4708941 C16.0385058,15.3211036 16.4028795,14.4157157 16.5604159,13.8223314 C17.1270934,15.1032789 17.9193087,15.5845732 18.2083143,15.1835909 C18.4451855,14.8542539 18.5199869,14.1146902 18.4956198,13.6073956 L18.4956198,13.6068179 Z"/>\n' +
                '</svg>
        },
        {
            socialName: 'Github',
            color: '#181818',
            svg:    <svg fill="white" viewBox="0 0 496 512" height="1.6em"><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z"></path></svg>
        },
        {
            socialName: 'Netease',
            color: '#ce7397',
            svg: <svg width="24px" height="24px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">\n' +
                '    <g>\n' +
                '        <path fill="none" d="M0 0h24v24H0z"/>\n' +
                '        <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-1.086-10.432c.24-.84 1.075-1.541 1.99-1.648.187.694.388 1.373.545 2.063.053.23.037.495-.018.727-.213.892-1.248 1.242-1.978.685-.53-.405-.742-1.12-.539-1.827zm3.817-.197c-.125-.465-.256-.927-.393-1.42.5.13.908.36 1.255.698 1.257 1.221 1.385 3.3.294 4.731-1.135 1.49-3.155 2.134-5.028 1.605-2.302-.65-3.808-2.952-3.441-5.316.274-1.768 1.27-3.004 2.9-3.733.407-.182.58-.56.42-.93-.157-.364-.54-.504-.944-.343-2.721 1.089-4.32 4.134-3.67 6.987.713 3.118 3.495 5.163 6.675 4.859 1.732-.165 3.164-.948 4.216-2.347 1.506-2.002 1.297-4.783-.463-6.499-.666-.65-1.471-1.018-2.39-1.153-.083-.013-.217-.052-.232-.106-.087-.313-.18-.632-.206-.954-.029-.357.29-.64.65-.645.253-.003.434.13.603.3.303.3.704.322.988.062.29-.264.296-.678.018-1.008-.566-.672-1.586-.891-2.43-.523-.847.37-1.321 1.187-1.2 2.093.038.28.11.557.167.842l-.26.072c-.856.24-1.561.704-2.098 1.414-.921 1.22-.936 2.828-.041 3.947 1.274 1.594 3.747 1.284 4.523-.568.284-.676.275-1.368.087-2.065z"/>\n' +
                '    </g>\n' +
                '</svg>
        },
        {
            socialName: 'Email',
            color: '#aec8c8',
            svg: <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 330.001 330.001" height="1.6em">
<g id="XMLID_348_">
	<path id="XMLID_350_" d="M173.871,177.097c-2.641,1.936-5.756,2.903-8.87,2.903c-3.116,0-6.23-0.967-8.871-2.903L30,84.602
		L0.001,62.603L0,275.001c0.001,8.284,6.716,15,15,15L315.001,290c8.285,0,15-6.716,15-14.999V62.602l-30.001,22L173.871,177.097z"
    />
    <polygon id="XMLID_351_" points="165.001,146.4 310.087,40.001 19.911,40 	"/>
</g>
</svg>
        }
    ]

    const social = SocialList.find(item => item.socialName === SocialName);

    if (!social) {
        return null; // Return null if the social name is not found
    }
    return <>
        <a className="SocialBtn" href={url} target="_blank">
            <span className="svgContainer">
                {social.svg}
            </span>
            <span className="BG" style={{background: `${social.color}`}}></span>
        </a>
    </>
}

export default SocialButton