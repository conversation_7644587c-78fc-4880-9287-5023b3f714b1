import './index.sass'
import {Avatar, Tag} from "antd";
import SocialButton from "../../../components/Buttons/SocialButton";
import {useEffect,  useState} from "react";
import {useSelector} from "react-redux";
import UserState from "../../../interface/UserState";
import { motion } from 'framer-motion';
import {formatNote, NoteType} from "../../../interface/NoteType";
import {categoryList} from "../../../store/components/categories.tsx";
import Article from "./Article.tsx";
import {useNavigate} from "react-router-dom";
import {SocialType} from "../../../interface/SocialType";
import {getNotePage, getTopNotes} from "../../../apis/NoteMethods.tsx";
const ContentHome = () => {
    // 临时简化组件，用于调试
    console.log('ContentHome 组件正在渲染');

    return (
        <div style={{
            minHeight: '100vh',
            backgroundColor: '#f0f0f0',
            padding: '20px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center'
        }}>
            <h1 style={{color: '#333', fontSize: '2rem', marginBottom: '20px'}}>
                🎉 Memory Blog 正在运行！
            </h1>
            <p style={{color: '#666', fontSize: '1.2rem', textAlign: 'center'}}>
                如果你看到这个页面，说明前端应用已经成功启动。<br/>
                后端服务器可能没有运行，但前端界面可以正常显示。
            </p>
            <div style={{
                marginTop: '30px',
                padding: '20px',
                backgroundColor: '#fff',
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}>
                <h3 style={{color: '#7880d1', marginBottom: '10px'}}>调试信息</h3>
                <p>✅ React 应用正常运行</p>
                <p>✅ 路由系统工作正常</p>
                <p>✅ 组件渲染成功</p>
                <p>⚠️ 后端 API 连接可能失败（这是正常的，如果后端没有运行）</p>
            </div>
        </div>
    );

    // 以下是原始代码，暂时注释掉
    /*
    const [currentTop,setCurrentTop] = useState(0)
    const [currentPage,setCurrentPage] = useState(1)
    const [hasMoreArticles, setHasMoreArticles] = useState(true);
    const [loading, setLoading] = useState(false);

    // 调试信息
    console.log('ContentHome 组件正在渲染');
    /*
    const avatar = useSelector((state:{user:UserState}) => state.user.avatar)
    const name = useSelector((state:{user:UserState}) => state.user.name)
    const oneSay = useSelector((state:{user:UserState}) => state.user.talk)
    const navigate = useNavigate()
    const [otherArticles,setOtherArticles] = useState<NoteType[]>([])
    const [topArticles,setTopArticles] = useState<NoteType[]>([])
    const Categories = useSelector((state: { categories: categoryList }) => state.categories.categories);
    const tagList = useSelector((state: {tags: any}) => state.tags.tag)
    const social = useSelector((state:{user:{social: SocialType}}) => state.user.social)
    const author =  useSelector((state: { user: UserState }) => state.user.name);

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTop(prevTop => (prevTop + 1) % topArticles.length);
        }, 3000);
        if(topArticles.length === 0)
            clearInterval(timer)
        return () => clearInterval(timer);
    },[currentTop])

    useEffect(() => {
        getNotePage({
            page: 1,
            pageSize: 6
        }).then(res => {
            setOtherArticles(res.data.data.map((item: formatNote) => {
                return {
                    ...item,
                    key: item.noteKey,
                    noteTags: item.noteTags ? item.noteTags.split(',').map(tag => parseInt(tag, 10)) : [],
                }
            }))
        }).catch(error => {
            console.warn('无法连接到后端服务器，使用默认数据:', error);
            // 设置一些默认的文章数据，这样页面仍然可以显示
            setOtherArticles([]);
        })
    }, []);
    */

    /*
    useEffect(() => {
        getTopNotes().then(res => {
            setTopArticles(res.data.data.map((item: formatNote) => {
                return {
                    ...item,
                    key: item.noteKey,
                    noteTags: item.noteTags ? item.noteTags.split(',').map(tag => parseInt(tag, 10)) : [],
                }
            }))
        }).catch(error => {
            console.warn('无法获取置顶文章，使用默认数据:', error);
            setTopArticles([]);
        })
    }, []);
    const handleScrollDown = () => {
        window.scrollTo({
            top: window.innerHeight,
            behavior: 'smooth'
        });
    }
    const getMore = () => {
        setLoading(true)
        getNotePage({
            page: currentPage + 1,
            pageSize: 6
        }).then(res => {
            if (res.data.data.length === 0) {
                setHasMoreArticles(false);
            } else {
                setCurrentPage(currentPage + 1);
                setOtherArticles(prevArticles => [
                    ...prevArticles,
                    ...res.data.data.map((item: formatNote) => ({
                        ...item,
                        key: item.noteKey,
                        noteTags: item.noteTags ? item.noteTags.split(',').map(tag => parseInt(tag, 10)) : [],
                    }))
                ]);
                if(res.data.data.length < 6)
                    setHasMoreArticles(false)
            }
        }).catch(error => {
            console.warn('无法加载更多文章:', error);
            setHasMoreArticles(false);
        }).finally(() => {
            setLoading(false);
        });
    };

    return <>
        <div className="SelfDescription" style={{backgroundColor: '#f0f0f0', minHeight: '100vh'}}>
            <div className="SayWords">
               <div>
                   <h2 style={{color: '#333'}}>Hi!👋</h2>
                   <h2 style={{color: '#333'}}>I'm <span style={{color: '#7880d1'}}>{author || 'Memory Blog'}</span></h2>
               </div>
                <h3 style={{color: '#666'}}>A Web Developer</h3>
                <div className="Social">
                    {/* 临时注释掉社交按钮，用于调试 */}
                    {/* <SocialButton SocialName='QQ' url={social?.socialQQ}/>
                    <SocialButton SocialName='Github' url={social?.socialGithub}/>
                    <SocialButton SocialName='Netease' url={social?.socialNeteaseCloud}/>
                    <SocialButton SocialName='Email' url={social?.socialEmail}/> */}
                    <p style={{color: '#999'}}>社交链接加载中...</p>
                </div>
            </div>
            {avatar && <Avatar src={avatar} size={320} className='frontAvatar'/>}
            <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1 }}
                style={{display:'flex',width:'200px',justifyContent:'center',bottom:'0',position:"absolute"}}
            >
            <p style={{position:'absolute',bottom:"100px", font: '600 12px ""', color: '#666'}}>{oneSay || '欢迎来到我的博客'}</p>
                <div style={{fontSize: 50,position:"absolute",bottom: 20,color:'skyblue', cursor: 'pointer'}} onClick={handleScrollDown}>⬇️</div>
            </motion.div>
        </div>
        <div className="ContentContainer dark-pic" style={{backgroundColor: '#fff', padding: '40px', minHeight: '50vh'}}>
            <div style={{textAlign: 'center', color: '#333'}}>
                <h2>博客内容区域</h2>
                <p>置顶文章数量: {topArticles.length}</p>
                <p>其他文章数量: {otherArticles.length}</p>
                <p>分类数量: {Categories.length}</p>
                <p>标签数量: {tagList.length}</p>

                {topArticles.length > 0 && (
                    <div style={{marginTop: '20px', padding: '20px', border: '1px solid #ddd', borderRadius: '8px'}}>
                        <h3>置顶文章预览</h3>
                        <p>标题: {topArticles[currentTop]?.noteTitle}</p>
                        <p>描述: {topArticles[currentTop]?.description}</p>
                    </div>
                )}

                {otherArticles.length > 0 && (
                    <div style={{marginTop: '20px', padding: '20px', border: '1px solid #ddd', borderRadius: '8px'}}>
                        <h3>文章列表</h3>
                        {otherArticles.slice(0, 3).map((item, index) => (
                            <div key={index} style={{margin: '10px 0', padding: '10px', backgroundColor: '#f9f9f9'}}>
                                <h4>{item.noteTitle}</h4>
                                <p>{item.description}</p>
                            </div>
                        ))}
                    </div>
                )}

                {loading && <p>加载中...</p>}
                {hasMoreArticles && !loading && (
                    <button onClick={getMore} style={{marginTop: '20px', padding: '10px 20px', backgroundColor: '#7880d1', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer'}}>
                        加载更多
                    </button>
                )}
            </div>
        </div>
    </>
    */
}

export default ContentHome