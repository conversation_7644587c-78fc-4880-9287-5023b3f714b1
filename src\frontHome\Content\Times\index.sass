.TimesContainer
  min-height: calc(100vh - 172.4px)
  width: 100%
  background: var(--container-background-color)
  display: flex
  align-items: center
  display: flex
  flex-direction: column
  padding-top: 100px
  .timePass
    height: 220px
    display: flex
    flex-direction: column
    justify-content: space-between
    margin-bottom: 50px
    @media only screen and (max-width: 800px)
      width: 80%
    hr
      width: 100px
      height: 3px
      background-color: #ce7397
    h2
      color:  var(--font-title-color)
      font-weight: 600
    h3
      color:  var(--font-title-color)
      font-weight: 500
    .Pass
      width: 100%
      color: #5a5a5a
      height: 100px
      display: flex
      flex-direction: column
      justify-content: space-between
      transition: 0.1ms inherit
.Link
  &:hover
    text-decoration: underline