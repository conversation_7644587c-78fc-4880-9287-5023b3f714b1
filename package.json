{"name": "Memory Blog", "private": true, "version": "1.0.1", "type": "module", "homepage": "./", "scripts": {"dev": "vite", "test": "jest", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/charts": "^2.0.3", "@ant-design/icons": "^5.3.6", "@ant-design/plots": "^2.1.15", "@botui/react": "^1.1.5", "@bytemd/plugin-breaks": "^1.21.0", "@bytemd/plugin-frontmatter": "^1.21.0", "@bytemd/plugin-gemoji": "^1.21.0", "@bytemd/plugin-gfm": "^1.21.0", "@bytemd/plugin-highlight": "^1.21.0", "@bytemd/plugin-medium-zoom": "^1.21.0", "@bytemd/react": "^1.21.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@fontsource/roboto": "^5.0.8", "@mui/icons-material": "^5.15.10", "@mui/material": "^5.15.10", "@reduxjs/toolkit": "^2.2.0", "antd": "^5.14.1", "axios": "^1.6.7", "botui": "^1.1.3", "bytemd": "^1.21.0", "dayjs": "^1.11.10", "framer-motion": "^11.0.20", "github-markdown-css": "^5.5.1", "gsap": "^3.12.5", "jest": "^29.7.0", "lodash": "^4.17.21", "markdown-navbar": "^1.4.3", "react": "^18.2.0", "react-countup": "^6.5.0", "react-dom": "^18.2.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^9.0.1", "react-redux": "^9.1.0", "react-router-dom": "^6.22.0", "react-syntax-highlighter": "^15.5.0", "react-tagcloud": "^2.3.3", "remark-gfm": "^4.0.0", "remark-toc": "^9.0.0", "scss": "^0.2.4", "typed.js": "^2.1.0"}, "devDependencies": {"@types/markdown-navbar": "^1.4.4", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/react-syntax-highlighter": "^15.5.11", "@types/react-tagcloud": "^2.3.2", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.70.0", "typescript": "^5.3.3", "vite": "^5.1.2", "vite-plugin-chunk-split": "^0.5.0", "vite-plugin-compression": "^0.5.1"}}