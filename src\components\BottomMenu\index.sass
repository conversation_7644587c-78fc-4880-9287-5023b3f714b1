@keyframes menuShow
  0%
    visibility: visible
    opacity: 0
  100%
    opacity: 1

@keyframes menuHide
  0%
    opacity: 1
  100%
    opacity: 0
    visibility: hidden

.bottomMenu
  display: none
  position: sticky
  bottom: 0
  right: 0
  z-index: 999
  @media only screen and (max-width: 1200px)
    display: block
  &.menuShow
    animation: menuShow 0.4s forwards
  &.menuHide
    animation: menuHide 0.4s forwards

  .menu-item,
  .menu-open-button
    background: #EEEEEE
    border-radius: 100%
    width: 45px
    height: 45px
    right: 20px
    bottom: 20px
    position: absolute
    color: #FFFFFF
    border: 0 solid
    text-align: center
    line-height: 80px
    -webkit-transform: translate3d(0, 0, 0)
    transform: translate3d(0, 0, 0)
    -webkit-transition: -webkit-transform ease-out 200ms
    transition: -webkit-transform ease-out 200ms
    transition: transform ease-out 200ms

  .menu-open
    display: none

  .lines
    width: 25px
    height: 3px
    background: #596778
    display: block
    position: absolute
    top: 50%
    left: 50%
    margin-left: -12.5px
    margin-top: -1.5px
    -webkit-transition: -webkit-transform 200ms
    transition: -webkit-transform 200ms
    transition: transform 200ms

  .line-1
    -webkit-transform: translate3d(0, -8px, 0)
    transform: translate3d(0, -8px, 0)

  .line-2
    -webkit-transform: translate3d(0, 0, 0)
    transform: translate3d(0, 0, 0)

  .line-3
    -webkit-transform: translate3d(0, 8px, 0)
    transform: translate3d(0, 8px, 0)

  .menu-open:checked + .menu-open-button .line-1
    -webkit-transform: translate3d(0, 0, 0) rotate(45deg)
    transform: translate3d(0, 0, 0) rotate(45deg)

  .menu-open:checked + .menu-open-button .line-2
    -webkit-transform: translate3d(0, 0, 0) scale(0.1, 1)
    transform: translate3d(0, 0, 0) scale(0.1, 1)

  .menu-open:checked + .menu-open-button .line-3
    -webkit-transform: translate3d(0, 0, 0) rotate(-45deg)
    transform: translate3d(0, 0, 0) rotate(-45deg)

  .menu
    margin: auto
    position: absolute
    top: 0
    bottom: 0
    left: 0
    right: 0
    width: 80px
    height: 80px
    text-align: center
    box-sizing: border-box
    font-size: 26px

  .menu-item:hover
    background: #EEEEEE
    color: #3290B1

  .menu-item:nth-child(3),
  .menu-item:nth-child(4),
  .menu-item:nth-child(5)
    background: transparent
    -webkit-transition-duration: 180ms
    transition-duration: 180ms

  .menu-open-button
    z-index: 2
    -webkit-transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275)
    transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275)
    -webkit-transition-duration: 400ms
    transition-duration: 400ms
    -webkit-transform: scale(1.1, 1.1) translate3d(0, 0, 0)
    transform: scale(1.1, 1.1) translate3d(0, 0, 0)
    cursor: pointer
    box-shadow: 3px 3px 0 0 rgba(0, 0, 0, 0.14)

  .menu-open-button:hover
    -webkit-transform: scale(1.2, 1.2) translate3d(0, 0, 0)
    transform: scale(1.2, 1.2) translate3d(0, 0, 0)

  .menu-open:checked + .menu-open-button
    -webkit-transition-timing-function: linear
    transition-timing-function: linear
    -webkit-transition-duration: 200ms
    transition-duration: 200ms
    -webkit-transform: scale(0.8, 0.8) translate3d(0, 0, 0)
    transform: scale(0.8, 0.8) translate3d(0, 0, 0)

  .menu-open:checked ~ .menu-item
    -webkit-transition-timing-function: cubic-bezier(0.935, 0, 0.34, 1.33)
    transition-timing-function: cubic-bezier(0.935, 0, 0.34, 1.33)

  .menu-open:checked ~ .menu-item:nth-child(3)
    background: transparent
    transition-duration: 180ms
    -webkit-transition-duration: 180ms
    -webkit-transform: translate3d(0.08361px, -54.99997px, 0)
    transform: translate3d(0.08361px, -47.99997px, 0)

  .menu-open:checked ~ .menu-item:nth-child(4)
    background: transparent
    transition-duration: 280ms
    -webkit-transition-duration: 280ms
    -webkit-transform: translate3d(60.9466px, -62.47586px, 0)
    transform: translate3d(0.08361px, -100px, 0)