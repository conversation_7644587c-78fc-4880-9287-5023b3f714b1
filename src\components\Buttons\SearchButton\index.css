.search_container {
    position: relative;
    box-sizing: border-box;
    width: fit-content;
}

.mainbox {
    box-sizing: border-box;
    position: relative;
    width: 230px;
    height: 50px;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    justify-content: center;
    border-radius: 160px;
    background-color: rgb(19, 168, 168);
    transition: all 0.3s ease;
}

.checkbox:focus {
    border: none;
    outline: none;
}

.checkbox:checked {
    right: 10px;
}

.checkbox:checked ~ .mainbox {
    width: 50px;
}

.checkbox:checked ~ .mainbox .search_input {
    width: 0;
    height: 0;
}

.checkbox:checked ~ .mainbox .iconContainer {
    padding-right: 8px;
}

.checkbox {
    box-sizing: border-box;
    width: 30px;
    height: 30px;
    position: absolute;
    right: 17px;
    top: 10px;
    z-index: 9;
    cursor: pointer;
    appearance: none;
}

.search_input {
    box-sizing: border-box;
    height: 100%;
    width: 170px;
    background-color: transparent;
    border: none;
    outline: none;
    padding-bottom: 4px;
    padding-left: 10px;
    font-size: 1em;
    color: white;
    transition: all 0.3s ease;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.search_input::placeholder {
    color: rgba(255, 255, 255, 0.776);
}

.iconContainer {
    box-sizing: border-box;
    padding-top: 5px;
    width: fit-content;
    transition: all 0.3s ease;
}

.search_icon {
    box-sizing: border-box;
    fill: white;
    font-size: 1.3em;
}